#include "debugmanage.h"
#include "IDEProjectAndFile/projectandfilemanage.h"
#include "IDEVariable/monitorlist.h"
#include "IDEVariable/variablemanage.h"
#include <QFuture>
#include <QtConcurrent>
#include <algorithm>

DebugManage &DebugManage::instance()
{
    static DebugManage m_instance;
    return m_instance;
}

void DebugManage::init(const QString &appPath)
{
    appDir = appPath;
}

void DebugManage::OpenProject(const QString &pName, const QString &pPath)
{
    qDebug() << "debugmanage" << "DebugManage::OpenProject";
    projectName = pName;
    projectDir = pPath.left(pPath.lastIndexOf("/")).replace("file:///", "");

    // readFile();
}

void DebugManage::clear()
{
    debugVarList.clear();
    debugDataTypeMap.clear();
    varListForSelect.clear();
}

int DebugManage::readFile()
{
    qDebug() << "debugmanage readFile";
    // 清除数据
    clear();
    // 获取项目编译后数据目录

    QString dir = projectDir + "/TASK";

    QString varFilePath = dir + "/all_data_test.csv";
    QString dataTypeFilePath = dir + "/all_data_flatten_st_info_bg.json";
    qDebug() << "debugmanage" << varFilePath << dataTypeFilePath;
    if (QFile::exists(varFilePath) && QFile::exists(dataTypeFilePath))
    {
        QFile varFile(varFilePath);
        if (!varFile.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            qDebug() << "debugmanage varFile open failed!";
            return 0;
        }
        QFile dataTypeFile(dataTypeFilePath);
        if (!dataTypeFile.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            qDebug() << "debugmanage dataTypeFile open failed!";
            return 0;
        }
        // 解析CSV
        QMap<QString, int> columnMap;
        QTextStream stream(&varFile);
        stream.setCodec("UTF-8");
        int index = 0;
        while (!stream.atEnd())
        {
            QString line = stream.readLine();
            // qDebug() << "debugmanage varFile line" << line;
            QStringList row = line.split(',');

            if (index > 0)
            {
                // qDebug() << "debugmanage row" << row << columnMap;

                // QSharedPointer<VariableList> newVar = QSharedPointer<VariableList>(new VariableList());
                QSharedPointer<VarTemp> newVar = QSharedPointer<VarTemp>(new VarTemp());
                newVar->id = index - 1;
                newVar->settingName = "";
                newVar->scope = row[columnMap.value("scope")];
                newVar->owned = row[columnMap.value("owned")];
                newVar->type = row[columnMap.value("type")];
                newVar->name = row[columnMap.value("name")];
                newVar->dataType = row[columnMap.value("datatype")];
                newVar->dataTypeID = 0;
                newVar->arrayLength = static_cast<int>(row[columnMap.value("arraylength")].toFloat());
                newVar->address = row[columnMap.value("address")];
                newVar->initialValue = row[columnMap.value("initialvalue")];
                newVar->isConstant = false;
                newVar->isOpc = false;
                newVar->isRetained = QVariant(row[columnMap.value("isretained")]).toBool();
                newVar->description = row[columnMap.value("description")];

                // qDebug() << "debugmanage" << newVar.id << newVar.name << newVar.dataType;

                debugVarList.append(newVar);
            }
            else
            {
                // 第一行列名，根据列名对应列进行读取
                for (int k = 0; k < row.size(); k++)
                {
                    columnMap.insert(row[k].toLower(), k);
                }
            }
            index++;
        }
        varFile.close();

        // 排序

        std::sort(debugVarList.begin(), debugVarList.end(),
                  [](QSharedPointer<VarTemp> &a, QSharedPointer<VarTemp> &b) { return a->id < b->id; });

        qDebug() << "debugmanage debugVarList size" << debugVarList.size();

        QString jsonData = dataTypeFile.readAll();
        dataTypeFile.close();

        QJsonParseError jsonError;
        QJsonDocument document = QJsonDocument::fromJson(jsonData.toUtf8(), &jsonError);
        if (jsonError.error != QJsonParseError::NoError)
        {
            qDebug() << "debugmanage JSON Parse error：" << jsonError.errorString();
            return 0;
        }

        QJsonObject jsonObject = document.object();
        qDebug() << "debugmanage JSON data" << jsonObject;
        // 根据数据类型JSON数据生成数据类型表 遍历所有属性
        QJsonObject::Iterator it;
        for (it = jsonObject.begin(); it != jsonObject.end(); it++)
        {
            // qDebug() << "debugmanage key value" << it.key() << it.value();

            QList<VariableType> typeList;
            QJsonArray ary = it.value().toArray();
            int sortn = 0;
            int olddeep = -1;
            QStack<int> parentidS;
            parentidS.push(sortn);
            for (auto row : ary)
            {
                // qDebug() << "debugmanage value array" << row;
                QJsonArray dataary = row.toArray();

                VariableType newType;
                newType.id = sortn;
                newType.settingName = "";
                newType.type = dataary.at(2).toString();
                newType.sortNumber = sortn;
                newType.name = dataary.at(0).toString();
                newType.shortName = "";
                newType.dataType = dataary.at(3).toString();
                newType.arrayCount = dataary.at(5).toInt();
                newType.deep = dataary.at(1).toInt();
                newType.bitLength = dataary.at(6).toInt() * 8;
                newType.mainOffset = dataary.at(7).toInt();
                newType.mainID = 0;

                if (newType.deep > olddeep)
                {
                    newType.parentID = parentidS.top();
                    parentidS.push(newType.id);
                }
                if (newType.deep == olddeep)
                {
                    parentidS.pop();
                    newType.parentID = parentidS.top();
                    parentidS.push(newType.id);
                }
                if (newType.deep < olddeep)
                {
                    for (int i = newType.deep; i >= olddeep; i--)
                    {
                        parentidS.pop();
                    }

                    newType.parentID = parentidS.top();
                    parentidS.push(newType.id);
                }

                // qDebug() << "debugmanage newType" << newType.id << newType.name << newType.type << newType.dataType
                // << newType.arrayCount  <<                         newType.deep << newType.parentID;
                typeList.append(newType);
                sortn++;
                olddeep = newType.deep;
            }
            if (typeList.size() > 0)
            {
                debugDataTypeMap.insert(it.key(), typeList);
            }
        }
        QFuture<void> future = QtConcurrent::run([=]() {
            qDebug() << __FUNCTION__ << QThread::currentThreadId() << QThread::currentThread();
            genVarListForSelect();
            // 同步已有记录
            QString devname = ProjectAndFileManage::instance().getCurrentDeviceName();
            syncSelectedVariableID(devname);
        });

        qDebug() << "debugmanage varListForSelect size" << varListForSelect.size();

        return index;
    }
    else
    {
        qDebug() << "debugmanage file no exist";
        return 0;
    }
}

void DebugManage::genVarListForSelect()
{
    varChildIndex = debugVarList.size();
    for (auto dy : debugVarList)
    {
        QJsonObject obj;
        int id = static_cast<int>(dy->id);
        QString vid = QString::number(dy->id);
        obj["ID"] = id;
        obj["VID"] = vid;
        obj["scope"] = dy->scope;
        obj["owned"] = dy->owned;
        obj["type"] = dy->type;
        obj["dataName"] = dy->name;
        obj["dataType"] = dy->dataType;
        obj["dataTypeID"] = static_cast<int>(dy->dataTypeID);
        obj["arrayLength"] = dy->arrayLength;
        obj["description"] = dy->description;
        obj["ParentID"] = 0;
        obj["Deep"] = 0;

        QJsonArray children;
        bool havechild = debugDataTypeMap.contains(dy->dataType) || dy->arrayLength > 1;
        obj["havechild"] = havechild;
        obj["chevron"] = !havechild;
        // 如果有子项，则添加子项数据
        if (havechild)
        {
            children = getChildVariableData(id, vid, dy->dataType);
            // qDebug() << "genVarListForSelect" << varListForSelect;
        }

        obj["children"] = children;
        varListForSelect.append(obj);
    }
    // qDebug() << "genVarListForSelect" << varListForSelect;
}

void DebugManage::syncSelectedVariableID(const QString &deviceName)
{
    // 根据新的数据源来更新已有监视表中的索引信息
    QList<MonitorList> monList = VariableManage::instance().getMonitorList(deviceName);
    for (auto &mon : monList)
    {
        QString parentName = mon.monitorName.split(".")[0].split("[")[0];
        bool findone = false;
        for (auto &var : debugVarList)
        {
            // 父级对应上
            if (var->name == parentName)
            {
                QString VID;
                if (var->name == mon.monitorName)
                {
                    VID = QString::number(var->id);
                }
                else
                {
                    // 进行子分类查找 递归
                    for (auto &vars : varListForSelect)
                    {
                        if (vars.value("dataName").toString() == parentName)
                        {
                            // qDebug() << "dddddd" << vars.value("dataName").toString() << parentName;
                            VID =
                                getVIDFromVarNameInVarListForSelect(vars.value("children").toArray(), mon.monitorName);

                            // qDebug() << "dddddddd" << mon.monitorName << VID;
                            break;
                        }
                    }
                }
                if (VID != "")
                {
                    mon.description = VID;
                    VariableManage::instance().modifyMonitorVariable(mon);
                    // qDebug() << "syncSelectedVariableID update" << mon.monitorName << mon.description;
                    findone = true;
                }
                break;
            }
        }
        if (!findone)
        {
            qDebug() << "debugmanage refresh var index no find " << mon.monitorName;
            // 失效的变量 该变量的值为 N/A
            mon.description = "N/A";
            mon.monitoredValue = "N/A";
            VariableManage::instance().modifyMonitorVariable(mon);
        }
    }
}

QString DebugManage::getVIDFromVarNameInVarListForSelect(const QJsonArray &ary, const QString &varName)
{
    for (auto obj : ary)
    {
        QJsonObject var = obj.toObject();
        // qDebug() << "getVIDFromVarNameInVarListForSelect" << var.value("dataName").toString() << varName;
        if (var.value("dataName").toString() == varName)
        {
            return var.value("VID").toString();
        }
        else
        {
            QString VID = getVIDFromVarNameInVarListForSelect(var.value("children").toArray(), varName);
            if (VID != "")
            {
                return VID;
            }
        }
    }
    return "";
}

QJsonArray DebugManage::getVariableListFromScopeAndOwned(const QString &deviceName, const QStringList &scope,
                                                         const QString &owned)
{
    qDebug() << "debugmanage getVariableListFromScopeAndOwned" << scope << owned << debugVarList.size();
    varChildIndex = debugVarList.size();
    QJsonArray array;
    if (!varListForSelect.isEmpty())
    {
        for (QJsonObject dy : varListForSelect)
        {
            // 筛选符合条件的变量
            // qDebug() << "debugmanage bj:" << dy.scope << dy.owned << scope.contains(dy.scope) << (owned == dy.owned);
            if (scope.contains(dy.value("scope").toString()) && owned == dy.value("owned").toString())
            {
                array.append(dy);
            }
        }
    }

    //    if (!debugVarList.isEmpty())
    //    {
    //        for (auto dy : debugVarList)
    //        {
    //            //筛选符合条件的变量
    //            //qDebug() << "debugmanage bj:" << dy.scope << dy.owned << scope.contains(dy.scope) << (owned ==
    //            dy.owned); if (scope.contains(dy.scope) && owned == dy.owned)
    //            {
    //                QJsonObject obj;
    //                obj["ID"] = static_cast<int>(dy.id);
    //                obj["vid"] = QString::number(dy.id);
    //                obj["scope"] = dy.scope;
    //                obj["owned"] = dy.owned;
    //                obj["type"] = dy.type;
    //                obj["name"] = dy.name;
    //                obj["dataType"] = dy.dataType;
    //                obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
    //                obj["arrayLength"] = dy.arrayLength;
    //                obj["description"] = dy.description;
    //                obj["ParentID"] = 0;
    //                obj["Deep"] = 0;
    //                obj["havechild"] = debugDataTypeMap.contains(dy.dataType) || dy.arrayLength > 1;
    //                bool havechild = debugDataTypeMap.contains(dy.dataType) || dy.arrayLength > 1;
    //                obj["havechild"] = havechild;
    //                obj["chevron"] = !havechild;
    //                array.append(obj);
    //            }
    //        }
    //    }
    return array;
}

QJsonArray DebugManage::getVariableListFromScopeAndType(const QString &deviceName, const QString &scope,
                                                        const QString &type)
{
    qDebug() << "debugmanage ownedList" << scope << type << debugVarList.size();
    varChildIndex = debugVarList.size();
    QJsonArray array;
    if (!varListForSelect.isEmpty())
    {
        for (QJsonObject dy : varListForSelect)
        {
            // 筛选符合条件的变量
            if (scope == dy.value("scope").toString() && type == dy.value("type").toString())
            {
                array.append(dy);
            }
        }
    }
    //    if (!debugVarList.isEmpty())
    //    {
    //        for (auto dy : debugVarList)
    //        {
    //            //筛选符合条件的变量
    //            if (scope == dy.scope && type == dy.type)
    //            {
    //                QJsonObject obj;
    //                obj["ID"] = static_cast<int>(dy.id);
    //                obj["vid"] = QString::number(dy.id);
    //                obj["scope"] = dy.scope;
    //                obj["owned"] = dy.owned;
    //                obj["type"] = dy.type;
    //                obj["name"] = dy.name;
    //                obj["dataType"] = dy.dataType;
    //                obj["dataTypeID"] = static_cast<int>(dy.dataTypeID);
    //                obj["arrayLength"] = dy.arrayLength;
    //                obj["description"] = dy.description;
    //                obj["ParentID"] = 0;
    //                obj["Deep"] = 0;
    //                obj["havechild"] = debugDataTypeMap.contains(dy.dataType) || dy.arrayLength > 1;
    //                array.append(obj);
    //            }
    //        }
    //    }
    return array;
}

QJsonArray DebugManage::getChildVariableData(int id, const QString &vid, const QString &datatype)
{
    // qDebug() << "debugmanage getChildVariableData" << vid << datatype;
    QJsonArray array;
    if (!debugVarList.isEmpty())
    {
        for (auto dy : debugVarList)
        {
            // 找到该变量
            if (vid == QString::number(dy->id))
            {
                // 先按数组展开
                if (dy->arrayLength > 1)
                {
                    for (int i = 0; i < dy->arrayLength; i++)
                    {
                        int newid = varChildIndex;
                        varChildIndex++;
                        QString newvid = QString::number(dy->id) + "_" + QString::number(i);
                        QString newname = dy->name + "[" + QString::number(i) + "]";

                        QJsonObject obj;
                        obj["ID"] = newid;
                        obj["VID"] = newvid;
                        obj["scope"] = dy->scope;
                        obj["owned"] = dy->owned;
                        obj["type"] = dy->type;
                        obj["dataName"] = newname;
                        obj["dataType"] = dy->dataType;
                        obj["dataTypeID"] = static_cast<int>(dy->dataTypeID);
                        obj["arrayLength"] = 1;
                        obj["description"] = dy->description;
                        obj["ParentID"] = id;
                        obj["Deep"] = 1;
                        bool havechild = debugDataTypeMap.contains(dy->dataType);
                        obj["havechild"] = havechild;
                        obj["chevron"] = !havechild;

                        // 获取子结构体展开
                        QJsonArray childAry;
                        if (debugDataTypeMap.contains(dy->dataType))
                        {
                            childAry = getTypeList(newid, newvid, newname, dy->dataType, 1);
                        }
                        obj["children"] = childAry;

                        array.append(obj);
                    }
                }
                else
                {
                    // 结构体展开
                    array = getTypeList(dy->id, QString::number(dy->id), dy->name, dy->dataType, 0);
                }
            }
        }
    }
    return array;
}

QJsonArray DebugManage::getTypeList(int id, const QString &vid, const QString &varname, const QString &dataType,
                                    int deep)
{
    QJsonArray ary;

    if (debugDataTypeMap.contains(dataType))
    {
        QList<VariableType> typeList = debugDataTypeMap.value(dataType);
        // 第一行总名称 略过 略过deep=0的
        int dindex = 0;
        for (int i = 1; i < typeList.size(); i++)
        {
            VariableType type = typeList.at(i);
            if (type.parentID == 0)
            {
                int newid = varChildIndex;
                varChildIndex++;
                QString newvid = vid + "_" + QString::number(dindex);
                QString newname = varname + "." + type.name;
                int newdeep = deep + 1;

                QJsonObject obj;
                obj["ID"] = newid;
                obj["VID"] = newvid;
                obj["scope"] = "";
                obj["owned"] = "";
                obj["type"] = "";
                obj["dataName"] = newname;
                obj["dataType"] = type.dataType;
                obj["dataTypeID"] = 0;
                obj["arrayLength"] = type.arrayCount;
                obj["description"] = type.description;
                obj["ParentID"] = id;
                obj["Deep"] = newdeep;
                bool havechild = type.arrayCount > 1 || haveChild(dataType, type.id);
                obj["havechild"] = havechild;
                obj["chevron"] = !havechild;
                // 获取子 如果是数组
                QJsonArray childAry;
                if (type.arrayCount > 1)
                {
                    for (int k = 0; k < type.arrayCount; k++)
                    {
                        int cnewid = varChildIndex;
                        varChildIndex++;
                        QString cnewvid = newvid + "_" + QString::number(k);
                        QString cnewname = newname + "[" + QString::number(k) + "]";
                        int cnewdeep = newdeep + 1;
                        QJsonObject cobj;
                        cobj["ID"] = cnewid;
                        cobj["VID"] = cnewvid;
                        cobj["scope"] = "";
                        cobj["owned"] = "";
                        cobj["type"] = "";
                        cobj["dataName"] = cnewname;
                        cobj["dataType"] = type.dataType;
                        cobj["dataTypeID"] = 0;
                        cobj["arrayLength"] = 1;
                        cobj["description"] = type.description;
                        cobj["ParentID"] = newid;
                        cobj["Deep"] = cnewdeep;
                        bool chavechild = haveChild(dataType, type.id);
                        cobj["havechild"] = chavechild;
                        cobj["chevron"] = !chavechild;
                        // 获取子
                        QJsonArray cchildAry;
                        if (chavechild)
                        {
                            cchildAry = getChildTypeList(cnewid, cnewvid, cnewname, dataType, cnewdeep, type.id);
                        }
                        cobj["children"] = cchildAry;

                        childAry.append(cobj);
                    }
                }
                else if (haveChild(dataType, type.id))
                {
                    childAry = getChildTypeList(newid, newvid, newname, type.dataType, newdeep, type.id);
                }
                obj["children"] = childAry;
                dindex++;
                ary.append(obj);
            }
        }
    }

    return ary;
}

QJsonArray DebugManage::getChildTypeList(int id, const QString &vid, const QString &varname, const QString &dataType,
                                         int deep, int structid)
{
    QJsonArray ary;
    if (debugDataTypeMap.contains(dataType))
    {
        QList<VariableType> typeList = debugDataTypeMap.value(dataType);

        for (int i = 0; i < typeList.size(); i++)
        {
            VariableType type = typeList.at(i);
            int dindex = 0;
            if (type.parentID == structid)
            {
                int newid = varChildIndex;
                varChildIndex++;
                QString newvid = vid + "_" + QString::number(dindex);
                QString newname = varname + "." + type.name;
                int newdeep = deep + 1;

                QJsonObject obj;
                obj["ID"] = newid;
                obj["VID"] = newvid;
                obj["scope"] = "";
                obj["owned"] = "";
                obj["type"] = "";
                obj["dataName"] = newname;
                obj["dataType"] = type.dataType;
                obj["dataTypeID"] = 0;
                obj["arrayLength"] = type.arrayCount;
                obj["description"] = type.description;
                obj["ParentID"] = id;
                obj["Deep"] = newdeep;
                bool havechild = type.arrayCount > 1 || haveChild(dataType, type.id);
                obj["havechild"] = havechild;
                obj["chevron"] = !havechild;

                // 获取子 如果是数组
                QJsonArray childAry;
                if (type.arrayCount > 1)
                {
                    for (int k = 0; k < type.arrayCount; k++)
                    {
                        int cnewid = varChildIndex;
                        varChildIndex++;
                        QString cnewvid = newvid + "_" + QString::number(k);
                        QString cnewname = newname + "[" + QString::number(k) + "]";
                        int cnewdeep = newdeep + 1;
                        QJsonObject cobj;
                        cobj["ID"] = cnewid;
                        cobj["VID"] = cnewvid;
                        cobj["scope"] = "";
                        cobj["owned"] = "";
                        cobj["type"] = "";
                        cobj["dataName"] = cnewname;
                        cobj["dataType"] = type.dataType;
                        cobj["dataTypeID"] = 0;
                        cobj["arrayLength"] = 1;
                        cobj["description"] = type.description;
                        cobj["ParentID"] = newid;
                        cobj["Deep"] = cnewdeep;
                        bool chavechild = haveChild(dataType, type.id);
                        cobj["havechild"] = chavechild;
                        cobj["chevron"] = !chavechild;

                        // 获取子
                        QJsonArray cchildAry;
                        if (chavechild)
                        {
                            cchildAry = getChildTypeList(cnewid, cnewvid, cnewname, dataType, cnewdeep, type.id);
                        }
                        cobj["children"] = cchildAry;

                        childAry.append(cobj);
                    }
                }
                else if (haveChild(dataType, type.id))
                {
                    childAry = getChildTypeList(newid, newvid, newname, type.dataType, newdeep, type.id);
                }
                obj["children"] = childAry;

                ary.append(obj);

                dindex++;
            }
        }
    }
    return ary;
}

bool DebugManage::haveChild(const QString &dataType, int parentid)
{
    bool flag = false;
    if (debugDataTypeMap.contains(dataType))
    {
        QList<VariableType> typeList = debugDataTypeMap.value(dataType);
        // 第一行总名称 略过 略过deep=0的
        for (int i = 1; i < typeList.size(); i++)
        {
            if (typeList.at(i).parentID == parentid)
            {
                flag = true;
                break;
            }
        }
    }

    return flag;
}
